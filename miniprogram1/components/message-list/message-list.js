/**
 * 通用消息列表组件
 * 支持点赞、评论、通知、发布等不同类型的消息列表
 */

const { messageAPI, handleError } = require('../../utils/api')
const { processMessageImages } = require('../../utils/imageUtil')
const { LIKE_TYPES } = require('../../utils/like-util.js')

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 消息类型：likes, replies, notifications
    type: {
      type: String,
      value: 'notifications'
    },
    // 页面标题
    title: {
      type: String,
      value: '消息列表'
    },
    // 空状态配置
    emptyConfig: {
      type: Object,
      value: {
        icon: '/images/xiaoxi.png',
        text: '暂无消息'
      }
    },
    // 是否自动加载
    autoLoad: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    messages: [],
    hiddenMessages: [], // 隐藏的消息（已读消息）
    loading: true,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    error: null,
    unreadCount: 0,
    showExpandHistory: false,
    loadingHistory: false,
    expandButtonText: '历史', // 按钮文字类型：'历史' 或 '剩余未读'
    unreadRemaining: 0 // 剩余未读数量
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 初始化加载
     */
    async onLoad() {
      if (this.properties.autoLoad) {
        await this.loadMessages(true)
      }
    },

    /**
     * 加载消息列表
     * @param {Boolean} refresh 是否刷新
     */
    async loadMessages(refresh = false) {
      if (this.data.loading && !refresh) return
      if (this.data.loadingMore && !refresh) return

      try {
        // 设置加载状态
        if (refresh) {
          this.setData({
            refreshing: true,
            page: 1,
            hasMore: true,
            error: null
          })
          // 触发加载开始事件
          this.triggerEvent('messagechange', {
            messages: [],
            loading: true
          })
        } else {
          this.setData({ loadingMore: true })
        }

        // 如果是首次加载，使用智能显示逻辑
        if (refresh) {
          const unreadCount = await this.getUnreadCount()
          this.setData({ unreadCount })

          if (unreadCount > 0) {
            // 有未读消息，使用智能加载
            await this.loadUnreadMessages(unreadCount)
            // 注意：不要在这里立即标记为已读，应该在用户真正查看后再标记
            return
          } else {
            // 无未读消息，正常显示第一页，显示展开历史按钮
            this.setData({
              showExpandHistory: true
            })
            // 无未读消息时，可以立即标记为已读
            this.markAsRead()
          }
        }

        // 正常分页加载
        const apiMethod = this.getApiMethod()
        const result = await apiMethod(this.data.page, this.data.pageSize)

        // 处理数据
        const newMessages = this.formatMessages(result.data || [])
        const messages = refresh ? newMessages : [...this.data.messages, ...newMessages]

        this.setData({
          messages,
          page: this.data.page + 1,
          hasMore: newMessages.length === this.data.pageSize,
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: null
        })

        // 触发数据变化事件
        this.triggerEvent('messagechange', {
          messages: messages,
          loading: false,
          showExpandHistory: this.data.showExpandHistory,
          loadingHistory: this.data.loadingHistory,
          hasMore: this.data.hasMore,
          loadingMore: this.data.loadingMore
        })



      } catch (error) {
        console.error('Load messages error:', error)
        this.setData({
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: handleError(error, false)
        })
      }
    },

    /**
     * 获取未读消息数量
     */
    async getUnreadCount() {
      try {
        const apiMethod = this.getApiMethod()
        const result = await apiMethod(1, 1) // 获取第一页，使用unread_count字段
        return result.unread_count || 0
      } catch (error) {
        console.error('获取未读数量失败:', error)
        return 0
      }
    },

    /**
     * 智能加载未读消息
     */
    async loadUnreadMessages(unreadCount) {
      try {
        const pageSize = this.data.pageSize

        if (unreadCount <= pageSize) {
          // 场景1：未读消息 ≤ 一页，只显示未读消息
          await this.loadUnreadOnly(unreadCount)
        } else {
          // 场景2：未读消息 > 一页，先显示第一页未读
          await this.loadUnreadPaged(unreadCount)
        }

      } catch (error) {
        console.error('加载未读消息失败:', error)
        // 失败时回退到正常加载
        this.loadMessages(true)
      }
    },

    /**
     * 场景1：加载少量未读消息（≤一页）
     */
    async loadUnreadOnly(unreadCount) {
      const apiMethod = this.getApiMethod()
      const result = await apiMethod(1, this.data.pageSize)
      const allMessages = this.formatMessages(result.data || [])

      // 前端智能显示：直接按未读数量显示前N条消息
      const unreadMessages = allMessages.slice(0, unreadCount)
      const remainingMessages = allMessages.slice(unreadCount)



      this.setData({
        messages: unreadMessages,
        hiddenMessages: remainingMessages, // 保存剩余消息
        page: 2,
        hasMore: result.has_more,
        showExpandHistory: remainingMessages.length > 0, // 有剩余消息才显示展开按钮
        expandButtonText: '历史',
        loading: false,
        refreshing: false,
        error: null
      })

      this.triggerDataChange()
    },

    /**
     * 场景2：加载大量未读消息（>一页）
     */
    async loadUnreadPaged(unreadCount) {
      const pageSize = this.data.pageSize
      const apiMethod = this.getApiMethod()
      const result = await apiMethod(1, pageSize)
      const allMessages = this.formatMessages(result.data || [])

      // 前端智能显示：直接按未读数量显示前N条消息
      const unreadMessages = allMessages.slice(0, unreadCount)
      const remainingMessages = allMessages.slice(unreadCount)



      this.setData({
        messages: unreadMessages,
        hiddenMessages: remainingMessages, // 保存已读消息
        page: 2,
        hasMore: result.has_more,
        showExpandHistory: true,
        expandButtonText: unreadMessages.length < unreadCount ? '剩余未读' : '历史',
        unreadRemaining: Math.max(0, unreadCount - unreadMessages.length),
        loading: false,
        refreshing: false,
        error: null
      })

      this.triggerDataChange()
    },

    /**
     * 触发数据变化事件
     */
    triggerDataChange() {
      this.triggerEvent('messagechange', {
        messages: this.data.messages,
        loading: false,
        showExpandHistory: this.data.showExpandHistory,
        loadingHistory: this.data.loadingHistory,
        hasMore: this.data.hasMore,
        loadingMore: this.data.loadingMore,
        expandButtonText: this.data.expandButtonText
      })
    },

    /**
     * 展开历史消息或剩余未读消息
     */
    async expandHistory() {
      if (this.data.loadingHistory) return

      this.setData({ loadingHistory: true })

      try {
        if (this.data.expandButtonText === '剩余未读') {
          // 继续加载剩余未读消息
          await this.loadMoreUnread()
        } else {
          // 展开当前页的剩余消息（已读消息）
          await this.showHiddenMessages()
        }

      } catch (error) {
        console.error('展开消息失败:', error)
        this.setData({ loadingHistory: false })
      }
    },

    /**
     * 加载更多未读消息
     */
    async loadMoreUnread() {
      const apiMethod = this.getApiMethod()
      const result = await apiMethod(this.data.page, this.data.pageSize)
      const allMessages = this.formatMessages(result.data || [])

      // 按剩余未读数量取消息
      const newUnreadMessages = allMessages.slice(0, this.data.unreadRemaining)
      const newReadMessages = allMessages.slice(this.data.unreadRemaining)

      const updatedUnreadRemaining = this.data.unreadRemaining - newUnreadMessages.length

      this.setData({
        messages: [...this.data.messages, ...newUnreadMessages],
        hiddenMessages: [...(this.data.hiddenMessages || []), ...newReadMessages],
        page: this.data.page + 1,
        unreadRemaining: Math.max(0, updatedUnreadRemaining),
        loadingHistory: false
      })

      // 如果没有剩余未读消息了，改为显示"展开历史"
      if (updatedUnreadRemaining <= 0) {
        this.setData({
          expandButtonText: '历史'
        })
      }

      this.triggerDataChange()
    },

    /**
     * 显示当前页的隐藏消息（已读消息）
     */
    async showHiddenMessages() {
      const hiddenMessages = this.data.hiddenMessages || []

      this.setData({
        messages: [...this.data.messages, ...hiddenMessages],
        hiddenMessages: [],
        showExpandHistory: false, // 隐藏展开按钮，显示正常的加载更多
        loadingHistory: false
      })

      // 用户展开历史消息时，标记所有消息为已读
      this.markAsRead()

      this.triggerDataChange()
    },

    /**
     * 获取对应的API方法
     */
    getApiMethod() {
      const apiMap = {
        likes: messageAPI.getLikes,
        replies: messageAPI.getReplies,
        notifications: messageAPI.getNotifications
      }
      return apiMap[this.properties.type] || messageAPI.getNotifications
    },

    /**
     * 格式化消息数据
     */
    formatMessages(messages) {
      return messages.map(item => {
        // 先处理图片URL
        const processedItem = processMessageImages(item)

        // 根据不同类型格式化数据
        switch (this.properties.type) {
          case 'likes':
            return this.formatLikeMessage(processedItem)
          case 'replies':
            // replies使用与likes相同的格式化逻辑，但动作文本不同
            return this.formatReplyMessage(processedItem)
          case 'notifications':
            return this.formatNotificationMessage(processedItem)
          // published类型已删除，使用专门的mypub页面
          default:
            return processedItem
        }
      })
    },

    /**
     * 格式化点赞消息
     */
    formatLikeMessage(item) {
      const { processAvatarUrl, processImageUrl } = require('../../utils/imageUtil')



      return {
        ...item,
        avatar: processAvatarUrl(item.from_user_avatar),
        username: item.from_username,
        actionText: this.getLikeActionText(item.target_type),
        time: item.created_at, // 后端已经格式化了时间，直接使用
        content: item.target_content,
        // 只有当有实际内容且内容不为空时才显示引用
        originalContent: (item.target_content && item.target_content.trim() !== '') ? item.target_content : null,
        originalImage: item.content_image ? processImageUrl(item.content_image) : null,
        image: item.content_image ? processImageUrl(item.content_image) : null
      }
    },

    /**
     * 格式化评论回复消息
     */
    formatReplyMessage(item) {
      const { processAvatarUrl, processImageUrl } = require('../../utils/imageUtil')
      return {
        ...item,
        avatar: processAvatarUrl(item.from_user_avatar),
        username: item.from_username,
        actionText: this.getReplyActionText(item.target_type),
        time: item.created_at, // 后端已经格式化了时间，直接使用
        content: item.target_content,
        // 只有当有实际内容且内容不为空时才显示引用
        originalContent: (item.target_content && item.target_content.trim() !== '') ? item.target_content : null,
        originalImage: item.content_image ? processImageUrl(item.content_image) : null,
        image: item.content_image ? processImageUrl(item.content_image) : null
      }
    },

    /**
     * 格式化系统通知消息
     */
    formatNotificationMessage(item) {
      const { processAvatarUrl } = require('../../utils/imageUtil')
      return {
        ...item,
        avatar: processAvatarUrl(item.from_user_avatar) || '/images/xiaoxi.png',
        username: item.from_username || '系统通知',
        actionText: item.content,
        time: item.created_at, // 后端已经格式化了时间，直接使用
        content: item.target_content || item.content
      }
    },

    // formatPublishedMessage函数已删除，使用专门的mypub页面

    /**
     * 获取点赞动作文本
     */
    getLikeActionText(targetType) {
      const actionMap = {
        comment: '赞了你的评论',
        reply: '赞了你的回复',
        message: '赞了你的帖子',
        liaoran_comment: '赞了你的了然几分评论',
        liaoran_reply: '赞了你的了然几分回复',
        window_comment: '赞了你的食堂评论',
        window_reply: '赞了你的食堂回复',
        major_comment: '赞了你的专业评论',
        major_reply: '赞了你的专业回复'
      }
      return actionMap[targetType] || '赞了你的内容'
    },

    /**
     * 获取评论回复动作文本
     */
    getReplyActionText(type) {
      const actionMap = {
        comment: '评论了你的帖子',
        reply: '回复了你的评论',
        reply_to_comment: '回复了你的评论',
        reply_to_reply: '回复了你的回复',
        liaoran_comment: '评论了你的了然几分对象',
        liaoran_reply: '回复了你的了然几分评论',
        liaoran_reply_to_reply: '回复了你的了然几分回复',
        window_comment: '评论了你的食堂窗口',
        window_reply: '回复了你的食堂评论',
        window_reply_to_reply: '回复了你的食堂回复',
        major_comment: '回复了你的专业评论',
        major_reply: '回复了你的专业回复',
        major_reply_to_reply: '回复了你的专业回复'
      }
      return actionMap[type] || '回复了你'
    },

    // getPublishedActionText函数已删除，使用专门的mypub页面

    /**
     * 获取原始内容
     */
    getOriginalContent(item) {
      switch (item.type) {
        case 'comment':
          return item.message_content
        case 'reply_to_comment':
          return item.original_content
        case 'reply_to_reply':
          return item.original_reply_content
        default:
          return item.original_content || item.message_content
      }
    },

    /**
     * 获取原始内容的图片
     */
    getOriginalImage(item) {
      switch (item.type) {
        case 'comment':
          return item.message_image
        case 'reply_to_comment':
          return item.original_image
        case 'reply_to_reply':
          return item.original_reply_image
        default:
          return item.original_image || item.message_image || item.content_image
      }
    },



    /**
     * 标记消息为已读
     */
    async markAsRead() {
      try {
        await messageAPI.markRead(this.properties.type)
      } catch (error) {
        console.error('Mark as read error:', error)
      }
    },

    /**
     * 标记所有消息为已读
     */
    async markAllAsRead() {
      try {
        await messageAPI.markRead(this.properties.type, { mark_all: true })
        // 刷新列表
        this.loadMessages(true)
      } catch (error) {
        console.error('Mark all as read error:', error)
      }
    },

    /**
     * 下拉刷新
     */
    onRefresh() {
      this.loadMessages(true)
    },

    /**
     * 点赞状态变化处理
     */
    onLikeChange(e) {
      const { type, targetId, isLiked, totalLikes } = e.detail

      // 更新本地数据
      const messages = this.data.messages
      const index = messages.findIndex(item => item.id === targetId)

      if (index !== -1) {
        messages[index].is_liked = isLiked
        messages[index].like_count = totalLikes

        this.setData({
          messages: messages
        })
      }

      // 触发父组件事件
      this.triggerEvent('likechange', e.detail)
    },

    /**
     * 上拉加载更多
     */
    onLoadMore() {
      if (this.data.hasMore && !this.data.loadingMore) {
        this.loadMessages(false)
      }
    },

    /**
     * 点击消息项
     */
    onMessageClick(e) {
      const { message } = e.currentTarget.dataset
      
      // 触发自定义事件
      this.triggerEvent('messageclick', {
        message,
        type: this.properties.type
      })
    },

    /**
     * 重试加载
     */
    onRetry() {
      this.setData({ error: null })
      this.loadMessages(true)
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.onLoad()
    }
  }
})
