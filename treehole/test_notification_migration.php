<?php
/**
 * Notification表图片迁移测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;
use app\service\ImageMigrationService;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');
$app->config->load($app->getConfigPath() . 'cos.php', 'cos');

class NotificationMigrationTest
{
    private $service;

    public function __construct()
    {
        $this->service = new ImageMigrationService();
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== Notification表图片迁移测试 ===\n\n";

        $this->testNotificationImageCount();
        $this->testSampleNotificationImages();
        $this->testNotificationMigration();

        echo "\n=== 测试完成 ===\n";
    }

    /**
     * 测试获取notification表图片总数
     */
    private function testNotificationImageCount()
    {
        echo "1. 测试获取notification表图片总数...\n";
        
        try {
            $totalImages = $this->service->getTotalImageCount('notification');
            echo "   Notification表需要迁移的图片总数: {$totalImages}\n";
            
            if ($totalImages >= 0) {
                echo "   ✓ 获取notification表图片总数功能正常\n";
            } else {
                echo "   ✗ 获取notification表图片总数异常\n";
            }
        } catch (\Exception $e) {
            echo "   ✗ 获取notification表图片总数失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试示例notification图片
     */
    private function testSampleNotificationImages()
    {
        echo "2. 测试示例notification图片...\n";
        
        try {
            // 获取所有可能包含图片的通知
            $allNotifications = Db::name('notification')
                ->where('content_image', '<>', '')
                ->where('content_image', 'not null')
                ->select();

            echo "   数据库中content_image字段不为空的通知总数: " . count($allNotifications) . "\n";

            // 筛选出真正包含图片URL的通知
            $validNotifications = [];
            foreach ($allNotifications as $notification) {
                $imageUrl = trim($notification['content_image']);
                if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    $validNotifications[] = $notification;
                }
            }

            echo "   真正包含图片的通知数量: " . count($validNotifications) . "\n";

            if (empty($validNotifications)) {
                echo "   ⚠️  没有找到包含有效图片的通知\n";
                echo "   建议检查数据库中的通知数据\n\n";
                return;
            }

            // 显示前5条真正包含图片的通知
            $displayNotifications = array_slice($validNotifications, 0, 5);
            
            foreach ($displayNotifications as $notification) {
                echo "   通知ID: {$notification['id']} (用户ID: {$notification['user_id']})\n";
                
                $imageUrl = trim($notification['content_image']);
                $isCos = $this->checkIfCosPath($imageUrl);
                echo "     图片: {$imageUrl}\n";
                echo "     状态: " . ($isCos ? 'COS路径' : '本地路径') . "\n";
                echo "     通知类型: {$notification['type']}\n";
                echo "     创建时间: {$notification['created_at']}\n";
                echo "\n";
            }

        } catch (\Exception $e) {
            echo "   ✗ 测试示例notification图片失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试notification表迁移功能
     */
    private function testNotificationMigration()
    {
        echo "3. 测试notification表迁移功能...\n";
        
        try {
            // 小批量测试迁移（只处理前3条记录）
            $result = $this->service->migrateImages('notification', 3, 0);
            
            if ($result['success']) {
                echo "   ✓ 迁移测试成功\n";
                echo "   处理记录数: {$result['processed']}\n";
                echo "   迁移成功数: {$result['migrated']}\n";
                echo "   迁移失败数: {$result['errors']}\n";
                echo "   消息: {$result['message']}\n";
            } else {
                echo "   ✗ 迁移测试失败: {$result['message']}\n";
            }

        } catch (\Exception $e) {
            echo "   ✗ 测试notification表迁移功能失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 检查是否是COS路径
     */
    private function checkIfCosPath($imageUrl)
    {
        return strpos($imageUrl, 'prod/') === 0 ||
               strpos($imageUrl, 'dev/') === 0 ||
               strpos($imageUrl, '.cos.') !== false ||
               strpos($imageUrl, 'treeholepublic-') !== false;
    }

    /**
     * 显示详细的notification表统计信息
     */
    public function showDetailedStats()
    {
        echo "=== Notification表详细统计 ===\n\n";

        try {
            // 总通知数
            $totalNotifications = Db::name('notification')->count();
            echo "总通知数: {$totalNotifications}\n";

            // 包含图片的通知数
            $withImageCount = Db::name('notification')
                ->where('content_image', '<>', '')
                ->where('content_image', 'not null')
                ->count();
            echo "包含图片字段的通知数: {$withImageCount}\n";

            // 已迁移到COS的通知数
            $migratedCount = Db::name('notification')
                ->where('content_image', 'like', '%cos.%')
                ->whereOr('content_image', 'like', 'prod/%')
                ->whereOr('content_image', 'like', 'dev/%')
                ->count();
            echo "已迁移到COS的通知数: {$migratedCount}\n";

            // 需要迁移的通知数
            $needMigrationCount = $this->service->getTotalImageCount('notification');
            echo "需要迁移的图片数: {$needMigrationCount}\n";

            // 迁移进度
            $progress = $withImageCount > 0 ? round(($migratedCount / $withImageCount) * 100, 2) : 0;
            echo "迁移进度: {$progress}%\n";

        } catch (\Exception $e) {
            echo "获取统计信息失败: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }
}

// 运行测试
$test = new NotificationMigrationTest();

// 检查命令行参数
if (isset($argv[1]) && $argv[1] === '--stats') {
    $test->showDetailedStats();
} else {
    $test->runAllTests();
}
