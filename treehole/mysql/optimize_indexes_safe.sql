-- 安全的数据库索引优化脚本
-- 兼容MySQL 5.6及以上版本，支持MyISAM存储引擎

-- 删除可能存在的旧存储过程
DROP PROCEDURE IF EXISTS SafeDropIndex;

-- 创建临时存储过程来安全地删除索引
DELIMITER $$

CREATE PROCEDURE SafeDropIndex(IN tableName VARCHAR(64), IN indexName VARCHAR(64))
BEGIN
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION BEGIN END;
    SET @sql = CONCAT('DROP INDEX `', indexName, '` ON `', tableName, '`');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END$$

DELIMITER ;

-- ================================
-- Comment表索引优化
-- ================================

-- 安全删除可能存在的旧索引
CALL SafeDropIndex('comment', 'idx_message_time_deleted');
CALL SafeDropIndex('comment', 'idx_user_time');
CALL SafeDropIndex('comment', 'idx_likes_time');
CALL SafeDropIndex('comment', 'idx_anonymous_message');

-- 添加新索引
ALTER TABLE `comment` ADD INDEX `idx_message_time_deleted` (`message_id`, `send_timestamp`, `is_deleted`);
ALTER TABLE `comment` ADD INDEX `idx_user_time` (`user_id`, `send_timestamp`);
ALTER TABLE `comment` ADD INDEX `idx_likes_time` (`total_likes`, `send_timestamp`);
ALTER TABLE `comment` ADD INDEX `idx_anonymous_message` (`is_from_anonymous_post`, `message_id`);

-- ================================
-- Post表索引优化
-- ================================

-- 安全删除可能存在的旧索引
CALL SafeDropIndex('post', 'idx_message_comment_time');
CALL SafeDropIndex('post', 'idx_comment_time_deleted');
CALL SafeDropIndex('post', 'idx_user_time');
CALL SafeDropIndex('post', 'idx_reply_chain');
CALL SafeDropIndex('post', 'idx_type_message_time');

-- 添加新索引
ALTER TABLE `post` ADD INDEX `idx_message_comment_time` (`message_id`, `comment_id`, `send_timestamp`);
ALTER TABLE `post` ADD INDEX `idx_comment_time_deleted` (`comment_id`, `send_timestamp`, `is_deleted`);
ALTER TABLE `post` ADD INDEX `idx_user_time` (`user_id`, `send_timestamp`);
ALTER TABLE `post` ADD INDEX `idx_reply_chain` (`reply_to_post_id`, `send_timestamp`);
ALTER TABLE `post` ADD INDEX `idx_type_message_time` (`reply_type`, `message_id`, `send_timestamp`);

-- ================================
-- Message表索引优化
-- ================================

-- 安全删除可能存在的旧索引
CALL SafeDropIndex('message', 'idx_choose_time_views');
CALL SafeDropIndex('message', 'idx_user_time');
CALL SafeDropIndex('message', 'idx_anonymous_time');
CALL SafeDropIndex('message', 'idx_vote_time');

-- 添加新索引
ALTER TABLE `message` ADD INDEX `idx_choose_time_views` (`choose`, `send_timestamp`, `views`);
ALTER TABLE `message` ADD INDEX `idx_user_time` (`user_id`, `send_timestamp`);
ALTER TABLE `message` ADD INDEX `idx_anonymous_time` (`is_anonymous`, `send_timestamp`);
ALTER TABLE `message` ADD INDEX `idx_vote_time` (`has_vote`, `send_timestamp`);

-- ================================
-- 统一点赞表索引优化
-- ================================

-- 安全删除可能存在的旧索引
CALL SafeDropIndex('unified_likes', 'idx_target_time');
CALL SafeDropIndex('unified_likes', 'idx_user_time');

-- 添加新索引
ALTER TABLE `unified_likes` ADD INDEX `idx_target_time` (`target_type`, `target_id`, `created_at`);
ALTER TABLE `unified_likes` ADD INDEX `idx_user_time` (`user_id`, `created_at`);

-- ================================
-- 通知表索引优化
-- ================================

-- 安全删除可能存在的旧索引
CALL SafeDropIndex('notification', 'idx_user_read_time');
CALL SafeDropIndex('notification', 'idx_type_time');

-- 添加新索引
ALTER TABLE `notification` ADD INDEX `idx_user_read_time` (`user_id`, `is_read`, `created_at`);
ALTER TABLE `notification` ADD INDEX `idx_type_time` (`type`, `created_at`);

-- 删除临时存储过程
DROP PROCEDURE SafeDropIndex;

-- ================================
-- 显示索引信息
-- ================================

SELECT 'Comment表索引:' as info;
SHOW INDEX FROM `comment`;

SELECT 'Post表索引:' as info;
SHOW INDEX FROM `post`;

SELECT 'Message表索引:' as info;
SHOW INDEX FROM `message`;

SELECT 'Unified_likes表索引:' as info;
SHOW INDEX FROM `unified_likes`;

SELECT 'Notification表索引:' as info;
SHOW INDEX FROM `notification`;

SELECT '索引优化完成!' as result;
