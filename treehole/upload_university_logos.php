<?php
/**
 * 上传大学校徽图片到腾讯云COS脚本
 * 将本地 treehole/public/中国所有大学校徽图片-200px-jpgs 文件夹中的所有图片上传到COS
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use app\util\CosUtil;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'cos.php', 'cos');

class UniversityLogoUploader
{
    private $localPath;
    private $cosPath;
    private $uploadedCount = 0;
    private $errorCount = 0;
    private $skippedCount = 0;

    public function __construct()
    {
        $this->localPath = __DIR__ . '/public/中国所有大学校徽图片-200px-jpgs';
        $this->cosPath = '中国所有大学校徽图片-200px-jpgs'; // COS中的文件夹路径
    }

    /**
     * 开始上传
     */
    public function upload()
    {
        $this->log("=== 开始上传大学校徽图片到COS ===");
        $this->log("本地路径: {$this->localPath}");
        $this->log("COS路径: {$this->cosPath}");

        // 检查本地文件夹是否存在
        if (!is_dir($this->localPath)) {
            $this->log("错误：本地文件夹不存在: {$this->localPath}");
            return;
        }

        // 获取所有图片文件
        $imageFiles = $this->getImageFiles();
        if (empty($imageFiles)) {
            $this->log("警告：没有找到图片文件");
            return;
        }

        $this->log("找到 " . count($imageFiles) . " 个图片文件");
        $this->log("开始上传...\n");

        foreach ($imageFiles as $index => $file) {
            $this->log("处理第 " . ($index + 1) . "/" . count($imageFiles) . " 个文件: {$file}");
            $this->uploadSingleFile($file);

            // 每上传5个文件休息一下，避免请求过于频繁
            if (($index + 1) % 5 === 0) {
                usleep(500000); // 0.5秒
            }
        }

        $this->log("\n=== 上传完成 ===");
        $this->log("成功上传: {$this->uploadedCount} 个文件");
        $this->log("跳过: {$this->skippedCount} 个文件");
        $this->log("失败: {$this->errorCount} 个文件");
    }

    /**
     * 获取所有图片文件
     */
    private function getImageFiles()
    {
        $imageFiles = [];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

        if ($handle = opendir($this->localPath)) {
            while (false !== ($file = readdir($handle))) {
                if ($file != "." && $file != "..") {
                    $filePath = $this->localPath . '/' . $file;
                    if (is_file($filePath)) {
                        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                        if (in_array($extension, $allowedExtensions)) {
                            $imageFiles[] = $file;
                        }
                    }
                }
            }
            closedir($handle);
        }

        // 按文件名排序
        sort($imageFiles);
        return $imageFiles;
    }

    /**
     * 上传单个文件
     */
    private function uploadSingleFile($fileName)
    {
        try {
            $localFilePath = $this->localPath . '/' . $fileName;
            $cosKey = $this->cosPath . '/' . $fileName;

            // 检查文件是否存在
            if (!file_exists($localFilePath)) {
                $this->log("  ✗ 文件不存在: {$localFilePath}");
                $this->errorCount++;
                return;
            }

            // 检查文件大小
            $fileSize = filesize($localFilePath);
            if ($fileSize === 0) {
                $this->log("  ✗ 文件为空: {$fileName}");
                $this->errorCount++;
                return;
            }

            // 检查COS中是否已存在该文件
            if ($this->checkFileExists($cosKey)) {
                $this->log("  ⚠ 文件已存在，跳过: {$fileName}");
                $this->skippedCount++;
                return;
            }

            // 上传文件到COS（使用自定义方法保持原始文件夹结构）
            $result = $this->uploadToCos($localFilePath, $cosKey);

            if ($result['success']) {
                $this->uploadedCount++;
                $this->log("  ✓ 上传成功: {$fileName} ({$this->formatFileSize($fileSize)})");
                $this->log("    COS路径: {$result['key']}");
            } else {
                $this->errorCount++;
                $this->log("  ✗ 上传失败: {$fileName}");
                $this->log("    错误信息: {$result['message']}");
            }

        } catch (\Exception $e) {
            $this->errorCount++;
            $this->log("  ✗ 上传异常: {$fileName}");
            $this->log("    异常信息: " . $e->getMessage());
        }
    }

    /**
     * 上传文件到COS（保持原始文件夹结构）
     */
    private function uploadToCos($localFilePath, $cosKey)
    {
        try {
            // 获取COS配置
            $config = \think\facade\Config::get('cos');

            // 创建COS客户端
            $client = new \Qcloud\Cos\Client([
                'region' => $config['region'],
                'schema' => 'https',
                'credentials' => [
                    'secretId' => $config['secret_id'],
                    'secretKey' => $config['secret_key'],
                ]
            ]);

            // 使用公有桶（假设大学校徽放在公有桶中）
            $bucket = $config['buckets']['public']['bucket'];

            $result = $client->putObject([
                'Bucket' => $bucket,
                'Key' => $cosKey,
                'Body' => fopen($localFilePath, 'rb'),
                'ACL' => 'public-read' // 公有读
            ]);

            return [
                'success' => true,
                'key' => $cosKey,
                'bucket_type' => 'public',
                'storage_type' => 'cos'
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'storage_type' => 'cos'
            ];
        }
    }

    /**
     * 检查COS中文件是否已存在
     */
    private function checkFileExists($cosKey)
    {
        try {
            // 获取COS配置
            $config = \think\facade\Config::get('cos');

            // 创建COS客户端
            $client = new \Qcloud\Cos\Client([
                'region' => $config['region'],
                'schema' => 'https',
                'credentials' => [
                    'secretId' => $config['secret_id'],
                    'secretKey' => $config['secret_key'],
                ]
            ]);

            $bucket = $config['buckets']['public']['bucket'];

            // 使用HEAD请求检查文件是否存在
            $client->headObject([
                'Bucket' => $bucket,
                'Key' => $cosKey
            ]);

            return true; // 如果没有抛出异常，说明文件存在

        } catch (\Exception $e) {
            // 如果抛出异常，通常说明文件不存在
            return false;
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }

    /**
     * 记录日志
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] {$message}\n";
    }

    /**
     * 显示本地文件统计信息
     */
    public function showStats()
    {
        $this->log("=== 本地文件统计 ===");
        
        if (!is_dir($this->localPath)) {
            $this->log("错误：本地文件夹不存在: {$this->localPath}");
            return;
        }

        $imageFiles = $this->getImageFiles();
        $this->log("本地路径: {$this->localPath}");
        $this->log("图片文件数量: " . count($imageFiles));

        if (!empty($imageFiles)) {
            $totalSize = 0;
            $extensions = [];

            foreach ($imageFiles as $file) {
                $filePath = $this->localPath . '/' . $file;
                $fileSize = filesize($filePath);
                $totalSize += $fileSize;

                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                $extensions[$extension] = ($extensions[$extension] ?? 0) + 1;
            }

            $this->log("总文件大小: " . $this->formatFileSize($totalSize));
            $this->log("文件类型分布:");
            foreach ($extensions as $ext => $count) {
                $this->log("  .{$ext}: {$count} 个文件");
            }

            $this->log("\n前10个文件:");
            $sampleFiles = array_slice($imageFiles, 0, 10);
            foreach ($sampleFiles as $file) {
                $filePath = $this->localPath . '/' . $file;
                $fileSize = filesize($filePath);
                $this->log("  {$file} ({$this->formatFileSize($fileSize)})");
            }

            if (count($imageFiles) > 10) {
                $this->log("  ... 还有 " . (count($imageFiles) - 10) . " 个文件");
            }
        }
    }
}

// 解析命令行参数
$showStats = false;
if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--stats' || $arg === '-s') {
            $showStats = true;
            break;
        }
    }
}

// 运行脚本
$uploader = new UniversityLogoUploader();

if ($showStats) {
    $uploader->showStats();
} else {
    echo "准备上传大学校徽图片到腾讯云COS...\n";
    echo "按 Enter 继续，或 Ctrl+C 取消...\n";
    readline();
    
    $uploader->upload();
}
