<?php
declare (strict_types = 1);

namespace app\service;

use think\facade\Db;
use app\util\CosUtil;
use think\facade\Log;

/**
 * 图片迁移服务类
 * 负责将本地图片迁移到COS
 */
class ImageMigrationService
{
    private $baseUrl = 'https://www.bjgaoxiaoshequ.store';
    
    /**
     * 迁移所有表的图片（message、comment、post）
     *
     * @param string $table 表名：message、comment、post 或 all
     * @param int $limit 每次处理的记录数量
     * @param int $offset 偏移量
     * @return array 迁移结果
     */
    public function migrateImages($table = 'all', $limit = 50, $offset = 0)
    {
        $result = [
            'success' => true,
            'processed' => 0,
            'migrated' => 0,
            'errors' => 0,
            'message' => '',
            'tables' => []
        ];

        try {
            if ($table === 'notification') {
                // 只迁移notification表
                $tableResult = $this->migrateNotificationTableImages($limit, $offset);
                $result = array_merge($result, $tableResult);
            } else {
                throw new \Exception("当前只支持notification表迁移，其他表已迁移完成");
            }

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['message'] = '迁移过程中发生错误：' . $e->getMessage();
            Log::error('图片迁移异常', ['error' => $e->getMessage()]);
        }

        return $result;
    }

    /**
     * 迁移Message表中的图片到COS（保持向后兼容）
     *
     * @param int $limit 每次处理的记录数量
     * @param int $offset 偏移量
     * @return array 迁移结果
     */
    public function migrateMessageImages($limit = 50, $offset = 0)
    {
        return $this->migrateTableImages('message', $limit, $offset);
    }

    /**
     * 迁移指定表的图片
     *
     * @param string $tableName 表名
     * @param int $limit 每次处理的记录数量
     * @param int $offset 偏移量
     * @return array 迁移结果
     */
    private function migrateTableImages($tableName, $limit = 50, $offset = 0)
    {
        $result = [
            'success' => true,
            'processed' => 0,
            'migrated' => 0,
            'errors' => 0,
            'message' => ''
        ];

        $result = [
            'success' => true,
            'processed' => 0,
            'migrated' => 0,
            'errors' => 0,
            'message' => ''
        ];

        try {
            // 只支持notification表
            if ($tableName === 'notification') {
                return $this->migrateNotificationTableImages($limit, $offset);
            } else {
                throw new \Exception("当前只支持notification表迁移，其他表已迁移完成");
            }

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['message'] = '迁移过程中发生错误：' . $e->getMessage();
            Log::error('图片迁移异常', ['table' => $tableName, 'error' => $e->getMessage()]);
        }

        return $result;
    }







    /**
     * 迁移notification表的图片（一次性处理所有记录）
     */
    private function migrateNotificationTableImages($limit = 0, $offset = 0)
    {
        $result = [
            'success' => true,
            'queried' => 0,      // 从数据库查询到的记录数
            'processed' => 0,    // 筛选后需要迁移的记录数
            'migrated' => 0,
            'errors' => 0,
            'message' => ''
        ];

        try {
            Log::info("开始查询notification表所有图片记录");

            // 查询所有包含图片的notification记录
            $notifications = Db::name('notification')
                ->where('content_image', '<>', '')
                ->where('content_image', 'not null')
                ->select();

            $result['queried'] = count($notifications);
            Log::info("查询到notification记录总数", ['count' => $result['queried']]);

            // 进一步筛选出真正包含有效图片的记录
            $validNotifications = [];
            $skippedCount = 0;
            $cosCount = 0;

            Log::info("开始筛选有效记录...");

            foreach ($notifications as $index => $notification) {
                $imageUrl = trim($notification['content_image']);

                // 每处理100条记录输出一次进度
                if (($index + 1) % 100 === 0) {
                    Log::info("筛选进度", ['processed' => $index + 1, 'total' => count($notifications)]);
                }

                if (empty($imageUrl)) {
                    $skippedCount++;
                    continue;
                }

                if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    $skippedCount++;
                    continue;
                }

                // 检查是否已经是COS路径
                if ($this->isCosPath($imageUrl)) {
                    $cosCount++;
                    continue;
                }

                $validNotifications[] = $notification;
            }

            Log::info("筛选结果", [
                'total_queried' => $result['queried'],
                'valid_for_migration' => count($validNotifications),
                'skipped_invalid' => $skippedCount,
                'already_cos' => $cosCount
            ]);

            $result['processed'] = count($validNotifications);

            if (count($validNotifications) > 0) {
                Log::info("开始迁移图片", ['total_to_migrate' => count($validNotifications)]);

                foreach ($validNotifications as $index => $notification) {
                    // 每处理10条记录输出一次进度
                    if (($index + 1) % 10 === 0) {
                        Log::info("迁移进度", [
                            'processed' => $index + 1,
                            'total' => count($validNotifications),
                            'migrated' => $result['migrated'],
                            'errors' => $result['errors']
                        ]);
                    }

                    $migrateResult = $this->migrateNotificationRecord($notification);
                    $result['migrated'] += $migrateResult['migrated'];
                    $result['errors'] += $migrateResult['errors'];

                    // 每迁移一张图片后稍微休息一下，避免服务器压力过大
                    if ($migrateResult['migrated'] > 0) {
                        usleep(100000); // 0.1秒
                    }
                }
            } else {
                Log::info("没有需要迁移的图片");
            }

            $result['message'] = "Notification表迁移完成：查询 {$result['queried']} 条，筛选 {$result['processed']} 条，迁移 {$result['migrated']} 个图片，失败 {$result['errors']} 个";
            Log::info($result['message']);

        } catch (\Exception $e) {
            $result['success'] = false;
            $result['message'] = 'Notification表迁移过程中发生错误：' . $e->getMessage();
            Log::error('Notification表图片迁移异常', ['error' => $e->getMessage()]);
        }

        return $result;
    }

    /**
     * 迁移单条notification记录的图片
     *
     * @param array $notification 通知记录
     * @return array 迁移结果
     */
    private function migrateNotificationRecord($notification)
    {
        $result = ['migrated' => 0, 'errors' => 0];

        $imageUrl = trim($notification['content_image']);
        if (empty($imageUrl)) {
            Log::warning("notification表记录 {$notification['id']} 的content_image字段为空");
            return $result;
        }

        // 检查是否已经是COS路径
        if ($this->isCosPath($imageUrl)) {
            Log::info("图片已经是COS路径，跳过", ['table' => 'notification', 'url' => $imageUrl]);
            return $result;
        }

        // 迁移图片
        $newPath = $this->migrateImage($imageUrl, $notification['id'], 'notification');
        if ($newPath) {
            // 更新数据库
            $this->updateNotificationImage($notification['id'], $newPath);
            $result['migrated']++;
            Log::info("图片迁移成功", ['table' => 'notification', 'old' => $imageUrl, 'new' => $newPath]);
        } else {
            $result['errors']++;
            Log::error("图片迁移失败", ['table' => 'notification', 'url' => $imageUrl]);
        }

        return $result;
    }

    /**
     * 更新notification记录的图片字段
     *
     * @param int $notificationId 通知ID
     * @param string $newImageUrl 新的图片URL
     * @return bool
     */
    private function updateNotificationImage($notificationId, $newImageUrl)
    {
        try {
            $result = Db::name('notification')
                ->where('id', $notificationId)
                ->update(['content_image' => $newImageUrl]);

            if ($result) {
                Log::info("更新notification表图片字段成功", ['notification_id' => $notificationId]);
                return true;
            } else {
                Log::error("更新notification表图片字段失败", ['notification_id' => $notificationId]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error("更新notification表图片字段异常", ['notification_id' => $notificationId, 'error' => $e->getMessage()]);
            return false;
        }
    }



    /**
     * 检查是否是COS路径
     *
     * @param string $imageUrl 图片URL
     * @return bool
     */
    private function isCosPath($imageUrl)
    {
        // 检查是否包含COS特征路径
        return strpos($imageUrl, 'prod/') === 0 ||
               strpos($imageUrl, 'dev/') === 0 ||
               strpos($imageUrl, '.cos.') !== false ||
               strpos($imageUrl, 'treeholepublic-') !== false;
    }

    /**
     * 迁移单个图片
     *
     * @param string $imageUrl 图片URL
     * @param int $recordId 记录ID
     * @param string $tableName 表名
     * @return string|false 成功返回COS路径，失败返回false
     */
    private function migrateImage($imageUrl, $recordId, $tableName = 'message')
    {
        try {
            // 构建完整的图片URL
            $fullUrl = $this->buildFullUrl($imageUrl);
            
            // 下载图片到临时文件
            $tempFile = $this->downloadImage($fullUrl);
            if (!$tempFile) {
                Log::error("下载图片失败", ['url' => $fullUrl]);
                return false;
            }

            // 生成新的文件名
            $extension = pathinfo(parse_url($imageUrl, PHP_URL_PATH), PATHINFO_EXTENSION);
            if (empty($extension)) {
                $extension = 'jpg'; // 默认扩展名
            }
            $fileName = "{$tableName}_{$recordId}_" . time() . "_" . rand(1000, 9999) . ".{$extension}";

            // 上传到COS
            $result = CosUtil::uploadFile('comment', $tempFile, $fileName);

            // 删除临时文件
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }

            if ($result['success']) {
                return $result['key']; // 返回COS相对路径
            } else {
                Log::error("图片上传到COS失败", ['url' => $imageUrl, 'error' => $result['message']]);
                return false;
            }

        } catch (\Exception $e) {
            Log::error("迁移图片异常", ['url' => $imageUrl, 'error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 构建完整的图片URL
     * 
     * @param string $imageUrl 图片URL
     * @return string
     */
    private function buildFullUrl($imageUrl)
    {
        // 如果已经是完整URL，直接返回
        if (strpos($imageUrl, 'http') === 0) {
            return $imageUrl;
        }

        // 如果是相对路径，添加域名
        return $this->baseUrl . (strpos($imageUrl, '/') === 0 ? '' : '/') . $imageUrl;
    }

    /**
     * 下载图片到临时文件
     * 
     * @param string $url 图片URL
     * @return string|false 成功返回临时文件路径，失败返回false
     */
    private function downloadImage($url)
    {
        try {
            $tempFile = tempnam(sys_get_temp_dir(), 'migrate_img_');
            
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'Mozilla/5.0 (compatible; ImageMigrator/1.0)',
                    'follow_location' => true,
                    'max_redirects' => 3
                ]
            ]);

            $imageData = file_get_contents($url, false, $context);
            if ($imageData === false) {
                Log::error("无法下载图片", ['url' => $url]);
                return false;
            }

            // 验证是否是有效的图片数据
            if (strlen($imageData) < 100) {
                Log::error("下载的图片数据太小", ['url' => $url, 'size' => strlen($imageData)]);
                return false;
            }

            file_put_contents($tempFile, $imageData);
            return $tempFile;

        } catch (\Exception $e) {
            Log::error("下载图片异常", ['url' => $url, 'error' => $e->getMessage()]);
            return false;
        }
    }



    /**
     * 获取需要迁移的图片总数
     *
     * @param string $table 表名：message、comment、post 或 all
     * @return int|array
     */
    public function getTotalImageCount($table = 'all')
    {
        try {
            if ($table === 'notification') {
                // 获取notification表的图片总数
                return $this->getTableImageCount($table);
            } else {
                throw new \Exception("当前只支持notification表，其他表已迁移完成");
            }

        } catch (\Exception $e) {
            Log::error("获取图片总数异常", ['table' => $table, 'error' => $e->getMessage()]);
            return 0;
        }
    }

    /**
     * 获取指定表的图片总数
     *
     * @param string $tableName 表名
     * @return int
     */
    private function getTableImageCount($tableName)
    {
        try {
            if ($tableName !== 'notification') {
                throw new \Exception("当前只支持notification表");
            }

            // notification表使用content_image字段，存储单张图片URL
            $query = Db::name($tableName)
                ->where('content_image', '<>', '')
                ->where('content_image', 'not null')
                ->field('content_image');

            $records = $query->select();

            $totalImages = 0;
            foreach ($records as $record) {
                $imageUrl = trim($record['content_image']);
                if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL) && !$this->isCosPath($imageUrl)) {
                    $totalImages++;
                }
            }

            return $totalImages;

        } catch (\Exception $e) {
            Log::error("获取{$tableName}表图片总数异常", ['table' => $tableName, 'error' => $e->getMessage()]);
            return 0;
        }
    }
}
