<?php
namespace app\util;

use Qcloud\Cos\Client;
use think\facade\Config;

class CosUtil
{
    private static $client = null;
    
    /**
     * 获取COS客户端实例
     */
    public static function getClient()
    {
        if (self::$client === null) {
            $config = Config::get('cos');
            self::$client = new Client([
                'region' => $config['region'],
                'credentials' => $config['credentials']
            ]);
        }
        return self::$client;
    }
    
    /**
     * 检查是否为本地环境
     */
    public static function isLocal()
    {
        return SecretUtil::isLocal();
    }
    
    /**
     * 上传文件到COS，返回相对路径
     */
    public static function uploadFile($fileType, $filePath, $fileName)
    {
        $config = Config::get('cos');

        // 强制使用COS，不管是本地还是生产环境
        // 注释掉本地环境检查，统一使用COS
        // if (self::isLocal() && !$config['local_config']['enable_cos']) {
        //     return self::uploadToLocal($fileType, $filePath, $fileName);
        // }

        try {
            $client = self::getClient();

            // 根据文件类型确定存储桶
            $bucketType = self::getBucketType($fileType);
            $bucket = $config['buckets'][$bucketType]['bucket'];

            // 构建对象键（相对路径）
            $key = self::buildObjectKey($fileType, $fileName);

            $result = $client->putObject([
                'Bucket' => $bucket,
                'Key' => $key,
                'Body' => fopen($filePath, 'rb'),
                'ACL' => $config['buckets'][$bucketType]['acl']
            ]);

            return [
                'success' => true,
                'key' => $key, // 只返回相对路径
                'bucket_type' => $bucketType,
                'storage_type' => 'cos'
            ];
            
        } catch (\Exception $e) {
            // 记录COS上传失败的详细信息
            \think\facade\Log::error('COS上传失败', [
                'fileType' => $fileType,
                'fileName' => $fileName,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 不再回退到本地存储，直接返回错误
            \think\facade\Log::error('COS上传失败，不回退到本地存储', ['fileType' => $fileType, 'fileName' => $fileName]);
            // if ($config['local_config']['fallback_to_local']) {
            //     \think\facade\Log::info('COS上传失败，回退到本地存储', ['fileType' => $fileType, 'fileName' => $fileName]);
            //     return self::uploadToLocal($fileType, $filePath, $fileName);
            // }

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 根据文件类型确定存储桶类型
     */
    private static function getBucketType($fileType)
    {
        $config = Config::get('cos');

        if (in_array($fileType, $config['file_mapping']['private_types'])) {
            return 'private';
        }

        return 'public';
    }

    /**
     * 构建对象键（文件在COS中的路径）
     */
    private static function buildObjectKey($fileType, $fileName)
    {
        // 为university-logos类型使用特殊的路径结构
        if ($fileType === 'university-logos') {
            return "中国所有大学校徽图片-200px-jpgs/{$fileName}";
        }

        // 其他文件类型使用原有的路径结构
        $envPrefix = self::isLocal() ? 'dev' : 'prod';
        $date = date('Y/m/d');
        return "{$envPrefix}/{$fileType}/{$date}/{$fileName}";
    }
    
    /**
     * 根据相对路径生成完整URL
     */
    public static function generateUrl($key, $bucketType = 'public')
    {
        if (empty($key)) return '';

        // 如果已经是完整URL，直接返回
        if (strpos($key, 'http') === 0) {
            return $key;
        }

        $config = Config::get('cos');

        // 本地环境使用COS直连域名，生产环境使用EdgeOne加速域名
        if (self::isLocal()) {
            $domain = $config['buckets'][$bucketType]['domains']['default'];
        } else {
            $domain = $config['buckets'][$bucketType]['domains']['production'] ??
                     $config['buckets'][$bucketType]['domains']['default'];
        }

        // 记录URL生成日志
        LogUtil::cos('generateUrl', $key, [
            'bucket_type' => $bucketType,
            'is_local' => self::isLocal(),
            'domain' => $domain,
            'final_url' => $domain . '/' . $key
        ]);

        return $domain . '/' . $key;
    }
    
    /**
     * 本地存储回退方案
     */
    private static function uploadToLocal($fileType, $filePath, $fileName)
    {
        // 为了兼容旧系统，头像直接使用touxiang路径，不带日期目录
        if ($fileType === 'avatar') {
            $uploadPath = "/touxiang/";
            $fullPath = app()->getRootPath() . 'public' . $uploadPath;
        } else {
            $uploadPath = "/uploads/{$fileType}/" . date('Y/m/d') . '/';
            $fullPath = app()->getRootPath() . 'public' . $uploadPath;
        }

        // 创建目录，忽略已存在的错误
        if (!is_dir($fullPath)) {
            @mkdir($fullPath, 0755, true);
            // 再次检查目录是否存在
            if (!is_dir($fullPath)) {
                return ['success' => false, 'message' => '无法创建目录: ' . $fullPath];
            }
        }

        $targetFile = $fullPath . $fileName;
        if (copy($filePath, $targetFile)) {
            $bucketType = self::getBucketType($fileType);

            \think\facade\Log::info('本地存储成功', [
                'fileType' => $fileType,
                'uploadPath' => $uploadPath,
                'fileName' => $fileName,
                'fullPath' => $fullPath
            ]);

            return [
                'success' => true,
                'key' => ltrim($uploadPath . $fileName, '/'), // 返回相对路径
                'bucket_type' => $bucketType,
                'storage_type' => 'local'
            ];
        }

        return ['success' => false, 'message' => '本地存储失败'];
    }

    /**
     * 生成私有文件的临时访问URL（用于敏感文件）
     */
    public static function generatePrivateUrl($key, $expireTime = 3600)
    {
        if (empty($key)) return '';

        // 如果已经是完整URL，直接返回
        if (strpos($key, 'http') === 0) {
            return $key;
        }

        $config = Config::get('cos');

        // 本地环境或非COS存储，使用普通URL
        if (self::isLocal()) {
            return self::generateUrl($key, 'private');
        }

        try {
            $client = self::getClient();
            $bucket = $config['buckets']['private']['bucket'];

            $signedUrl = $client->getPresignedUrl('GetObject', [
                'Bucket' => $bucket,
                'Key' => $key
            ], "+{$expireTime} seconds");

            return $signedUrl;
        } catch (\Exception $e) {
            // 失败时返回普通URL
            return self::generateUrl($key, 'private');
        }
    }
    
    /**
     * 批量处理图片URL
     */
    public static function processImageUrls($images, $bucketType = 'public')
    {
        if (empty($images)) return [];
        
        if (is_string($images)) {
            $images = json_decode($images, true);
        }
        
        if (!is_array($images)) return [];
        
        return array_map(function($image) use ($bucketType) {
            return self::generateUrl($image, $bucketType);
        }, $images);
    }
}
