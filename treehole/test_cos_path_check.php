<?php
/**
 * 测试COS路径检查逻辑
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;
use app\service\ImageMigrationService;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');

echo "=== COS路径检查测试 ===\n\n";

// 测试URL示例
$testUrls = [
    'https://www.bjgaoxiaoshequ.store/file/2025-03-10/msg_1741598754_1104611993.jpg',
    'https://www.bjgaoxiaoshequ.store/comment_image/2025-05-18/comment_1747543383_1409493283.jpg',
    'prod/notification/notification_123_1733123456_7890.jpg',
    'dev/notification/notification_123_1733123456_7890.jpg',
    'https://treeholepublic-1234567890.cos.ap-beijing.myqcloud.com/prod/notification/test.jpg',
    'https://example.cos.ap-beijing.myqcloud.com/test.jpg'
];

$service = new ImageMigrationService();

// 使用反射来访问私有方法
$reflection = new ReflectionClass($service);
$isCosPathMethod = $reflection->getMethod('isCosPath');
$isCosPathMethod->setAccessible(true);

echo "测试COS路径检查逻辑:\n";
foreach ($testUrls as $url) {
    $isCos = $isCosPathMethod->invoke($service, $url);
    echo "  URL: {$url}\n";
    echo "  是否COS路径: " . ($isCos ? '是' : '否') . "\n";
    echo "  详细检查:\n";
    echo "    - 以prod/开头: " . (strpos($url, 'prod/') === 0 ? '是' : '否') . "\n";
    echo "    - 以dev/开头: " . (strpos($url, 'dev/') === 0 ? '是' : '否') . "\n";
    echo "    - 包含.cos.: " . (strpos($url, '.cos.') !== false ? '是' : '否') . "\n";
    echo "    - 包含treeholepublic-: " . (strpos($url, 'treeholepublic-') !== false ? '是' : '否') . "\n";
    echo "\n";
}

echo "=== 实际数据库数据测试 ===\n\n";

// 获取前10条notification记录进行测试
$notifications = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->limit(10)
    ->select();

echo "测试前10条notification记录:\n";
foreach ($notifications as $notification) {
    echo "  ID: {$notification['id']}\n";
    echo "  原始数据库值: '{$notification['content_image']}'\n";
    echo "  原始数据长度: " . strlen($notification['content_image']) . "\n";
    echo "  原始数据十六进制: " . bin2hex($notification['content_image']) . "\n";

    $imageUrl = trim($notification['content_image']);
    echo "  trim后的值: '{$imageUrl}'\n";
    echo "  trim后长度: " . strlen($imageUrl) . "\n";

    $isCos = $isCosPathMethod->invoke($service, $imageUrl);
    $isValidUrl = filter_var($imageUrl, FILTER_VALIDATE_URL);

    echo "  是否有效URL: " . ($isValidUrl ? '是' : '否') . "\n";
    if (!$isValidUrl) {
        echo "  URL验证失败原因: " . (empty($imageUrl) ? '空字符串' : '格式不正确') . "\n";
    }
    echo "  是否COS路径: " . ($isCos ? '是' : '否') . "\n";
    echo "  是否需要迁移: " . (!$isCos && $isValidUrl ? '是' : '否') . "\n";
    echo "  ----------------------------------------\n";
}

// 统计需要迁移的数量
$needMigrationCount = 0;
$totalWithImage = 0;
$invalidUrls = 0;
$alreadyCos = 0;

$allNotifications = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->select();

foreach ($allNotifications as $notification) {
    $totalWithImage++;
    $imageUrl = trim($notification['content_image']);
    
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        $invalidUrls++;
        continue;
    }
    
    if ($isCosPathMethod->invoke($service, $imageUrl)) {
        $alreadyCos++;
        continue;
    }
    
    $needMigrationCount++;
}

echo "=== 统计结果 ===\n";
echo "总计包含图片的通知: {$totalWithImage}\n";
echo "无效URL: {$invalidUrls}\n";
echo "已经是COS路径: {$alreadyCos}\n";
echo "需要迁移: {$needMigrationCount}\n";
echo "\n=== 测试完成 ===\n";
