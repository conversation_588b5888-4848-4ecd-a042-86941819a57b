<?php
/**
 * 调试第一批notification记录
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;
use app\service\ImageMigrationService;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');

echo "=== 调试第一批notification记录 ===\n\n";

// 模拟第一批查询
$notifications = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->limit(0, 10)  // 只看前10条
    ->select();

echo "查询到 " . count($notifications) . " 条记录\n\n";

$service = new ImageMigrationService();

// 使用反射来访问私有方法
$reflection = new ReflectionClass($service);
$isCosPathMethod = $reflection->getMethod('isCosPath');
$isCosPathMethod->setAccessible(true);

foreach ($notifications as $index => $notification) {
    echo "=== 记录 " . ($index + 1) . " ===\n";
    echo "ID: {$notification['id']}\n";
    echo "原始URL: '{$notification['content_image']}'\n";
    echo "URL长度: " . strlen($notification['content_image']) . "\n";
    
    $imageUrl = trim($notification['content_image']);
    echo "trim后URL: '{$imageUrl}'\n";
    echo "trim后长度: " . strlen($imageUrl) . "\n";
    
    // 检查是否为空
    $isEmpty = empty($imageUrl);
    echo "是否为空: " . ($isEmpty ? '是' : '否') . "\n";
    
    if (!$isEmpty) {
        // URL验证
        $isValidUrl = filter_var($imageUrl, FILTER_VALIDATE_URL);
        echo "URL验证: " . ($isValidUrl ? '通过' : '失败') . "\n";
        
        if ($isValidUrl) {
            // COS路径检查
            $isCos = $isCosPathMethod->invoke($service, $imageUrl);
            echo "COS路径检查: " . ($isCos ? '是COS路径' : '不是COS路径') . "\n";
            
            // 详细检查
            echo "详细检查:\n";
            echo "  - 以prod/开头: " . (strpos($imageUrl, 'prod/') === 0 ? '是' : '否') . "\n";
            echo "  - 以dev/开头: " . (strpos($imageUrl, 'dev/') === 0 ? '是' : '否') . "\n";
            echo "  - 包含.cos.: " . (strpos($imageUrl, '.cos.') !== false ? '是' : '否') . "\n";
            echo "  - 包含treeholepublic-: " . (strpos($imageUrl, 'treeholepublic-') !== false ? '是' : '否') . "\n";
            
            echo "最终判断: " . (!$isCos ? '需要迁移' : '跳过') . "\n";
        }
    }
    
    echo "\n";
}

// 统计第一批的情况
$needMigration = 0;
$alreadyCos = 0;
$invalid = 0;
$empty = 0;

foreach ($notifications as $notification) {
    $imageUrl = trim($notification['content_image']);
    
    if (empty($imageUrl)) {
        $empty++;
        continue;
    }
    
    if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
        $invalid++;
        continue;
    }
    
    if ($isCosPathMethod->invoke($service, $imageUrl)) {
        $alreadyCos++;
        continue;
    }
    
    $needMigration++;
}

echo "=== 第一批统计 ===\n";
echo "总记录数: " . count($notifications) . "\n";
echo "空URL: {$empty}\n";
echo "无效URL: {$invalid}\n";
echo "已是COS路径: {$alreadyCos}\n";
echo "需要迁移: {$needMigration}\n";

if ($needMigration == 0) {
    echo "\n⚠️  第一批没有需要迁移的记录！\n";
    echo "这解释了为什么筛选结果为0。\n";
    echo "建议检查更多批次或调整查询条件。\n";
}

echo "\n=== 调试完成 ===\n";
