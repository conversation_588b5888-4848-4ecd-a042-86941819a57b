<?php
/**
 * 修复版大学校徽上传脚本
 * 使用修改后的CosUtil，支持自定义文件夹结构
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use app\util\CosUtil;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'cos.php', 'cos');

class FixedLogoUploader
{
    private $localPath;
    private $uploadedCount = 0;
    private $errorCount = 0;
    private $skippedCount = 0;

    public function __construct()
    {
        $this->localPath = __DIR__ . '/public/中国所有大学校徽图片-200px-jpgs';
    }

    /**
     * 开始上传
     */
    public function upload()
    {
        $this->log("=== 开始上传大学校徽图片到COS ===");
        $this->log("本地路径: {$this->localPath}");
        $this->log("COS路径: 中国所有大学校徽图片-200px-jpgs/");

        // 检查本地文件夹是否存在
        if (!is_dir($this->localPath)) {
            $this->log("错误：本地文件夹不存在: {$this->localPath}");
            return;
        }

        // 获取所有图片文件
        $imageFiles = $this->getImageFiles();
        if (empty($imageFiles)) {
            $this->log("警告：没有找到图片文件");
            return;
        }

        $this->log("找到 " . count($imageFiles) . " 个图片文件");
        $this->log("开始上传...\n");

        foreach ($imageFiles as $index => $file) {
            $this->log("处理第 " . ($index + 1) . "/" . count($imageFiles) . " 个文件: {$file}");
            $this->uploadSingleFile($file);

            // 每上传10个文件休息一下
            if (($index + 1) % 10 === 0) {
                usleep(500000); // 0.5秒
                $this->log("  已处理 " . ($index + 1) . " 个文件，休息0.5秒...");
            }
        }

        $this->log("\n=== 上传完成 ===");
        $this->log("成功上传: {$this->uploadedCount} 个文件");
        $this->log("跳过: {$this->skippedCount} 个文件");
        $this->log("失败: {$this->errorCount} 个文件");
    }

    /**
     * 获取所有图片文件
     */
    private function getImageFiles()
    {
        $imageFiles = [];
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

        if ($handle = opendir($this->localPath)) {
            while (false !== ($file = readdir($handle))) {
                if ($file != "." && $file != "..") {
                    $filePath = $this->localPath . '/' . $file;
                    if (is_file($filePath)) {
                        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                        if (in_array($extension, $allowedExtensions)) {
                            $imageFiles[] = $file;
                        }
                    }
                }
            }
            closedir($handle);
        }

        sort($imageFiles);
        return $imageFiles;
    }

    /**
     * 上传单个文件
     */
    private function uploadSingleFile($fileName)
    {
        try {
            $localFilePath = $this->localPath . '/' . $fileName;

            // 检查文件是否存在
            if (!file_exists($localFilePath)) {
                $this->log("  ✗ 文件不存在: {$localFilePath}");
                $this->errorCount++;
                return;
            }

            $fileSize = filesize($localFilePath);
            if ($fileSize === 0) {
                $this->log("  ✗ 文件为空: {$fileName}");
                $this->errorCount++;
                return;
            }

            // 使用修改后的CosUtil上传，文件类型为 'university-logos'
            // 这会自动使用 "中国所有大学校徽图片-200px-jpgs/{$fileName}" 作为COS路径
            $result = CosUtil::uploadFile('university-logos', $localFilePath, $fileName);

            if ($result['success']) {
                $this->uploadedCount++;
                $this->log("  ✓ 上传成功: {$fileName} ({$this->formatFileSize($fileSize)})");
                $this->log("    COS路径: {$result['key']}");
                
                // 生成访问URL
                $url = CosUtil::generateUrl($result['key'], $result['bucket_type']);
                $this->log("    访问URL: {$url}");
            } else {
                $this->errorCount++;
                $this->log("  ✗ 上传失败: {$fileName}");
                if (isset($result['message'])) {
                    $this->log("    错误信息: {$result['message']}");
                }
            }

        } catch (\Exception $e) {
            $this->errorCount++;
            $this->log("  ✗ 上传异常: {$fileName}");
            $this->log("    异常信息: " . $e->getMessage());
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return round($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return round($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }

    /**
     * 记录日志
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        echo "[{$timestamp}] {$message}\n";
    }

    /**
     * 显示本地文件统计信息
     */
    public function showStats()
    {
        $this->log("=== 本地文件统计 ===");
        
        if (!is_dir($this->localPath)) {
            $this->log("错误：本地文件夹不存在: {$this->localPath}");
            return;
        }

        $imageFiles = $this->getImageFiles();
        $this->log("本地路径: {$this->localPath}");
        $this->log("图片文件数量: " . count($imageFiles));

        if (!empty($imageFiles)) {
            $totalSize = 0;
            $extensions = [];

            foreach ($imageFiles as $file) {
                $filePath = $this->localPath . '/' . $file;
                $fileSize = filesize($filePath);
                $totalSize += $fileSize;

                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                $extensions[$extension] = ($extensions[$extension] ?? 0) + 1;
            }

            $this->log("总文件大小: " . $this->formatFileSize($totalSize));
            $this->log("文件类型分布:");
            foreach ($extensions as $ext => $count) {
                $this->log("  .{$ext}: {$count} 个文件");
            }

            $this->log("\n前10个文件:");
            $sampleFiles = array_slice($imageFiles, 0, 10);
            foreach ($sampleFiles as $file) {
                $filePath = $this->localPath . '/' . $file;
                $fileSize = filesize($filePath);
                $this->log("  {$file} ({$this->formatFileSize($fileSize)})");
            }

            if (count($imageFiles) > 10) {
                $this->log("  ... 还有 " . (count($imageFiles) - 10) . " 个文件");
            }
        }
    }

    /**
     * 测试上传单个文件
     */
    public function testUpload()
    {
        $this->log("=== 测试上传功能 ===");
        
        $imageFiles = $this->getImageFiles();
        if (empty($imageFiles)) {
            $this->log("没有找到图片文件进行测试");
            return;
        }

        // 选择第一个文件进行测试
        $testFile = $imageFiles[0];
        $this->log("测试文件: {$testFile}");
        
        $this->uploadSingleFile($testFile);
        
        if ($this->uploadedCount > 0) {
            $this->log("✓ 测试上传成功！可以继续批量上传");
        } else {
            $this->log("✗ 测试上传失败，请检查配置");
        }
    }
}

// 解析命令行参数
$action = 'upload';
if (isset($argv[1])) {
    switch ($argv[1]) {
        case '--stats':
        case '-s':
            $action = 'stats';
            break;
        case '--test':
        case '-t':
            $action = 'test';
            break;
        case '--help':
        case '-h':
            $action = 'help';
            break;
    }
}

// 运行脚本
$uploader = new FixedLogoUploader();

switch ($action) {
    case 'stats':
        $uploader->showStats();
        break;
    case 'test':
        $uploader->testUpload();
        break;
    case 'help':
        echo "大学校徽上传脚本使用说明：\n";
        echo "  php upload_logos_fixed.php           # 批量上传所有文件\n";
        echo "  php upload_logos_fixed.php --stats   # 显示本地文件统计\n";
        echo "  php upload_logos_fixed.php --test    # 测试上传单个文件\n";
        echo "  php upload_logos_fixed.php --help    # 显示帮助信息\n\n";
        echo "上传路径: 中国所有大学校徽图片-200px-jpgs/\n";
        break;
    default:
        echo "准备批量上传大学校徽图片到腾讯云COS...\n";
        echo "目标路径: 中国所有大学校徽图片-200px-jpgs/\n";
        echo "按 Enter 继续，或 Ctrl+C 取消...\n";
        readline();
        
        $uploader->upload();
        break;
}
