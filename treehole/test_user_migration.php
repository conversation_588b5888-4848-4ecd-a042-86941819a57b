<?php
/**
 * User表图片迁移测试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;
use app\service\ImageMigrationService;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');
$app->config->load($app->getConfigPath() . 'cos.php', 'cos');

class UserMigrationTest
{
    private $service;

    public function __construct()
    {
        $this->service = new ImageMigrationService();
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "=== User表图片迁移测试 ===\n\n";

        $this->testUserImageCount();
        $this->testSampleUserImages();
        $this->testRelatedTablesCount();

        echo "\n=== 测试完成 ===\n";
    }

    /**
     * 测试获取user表图片总数
     */
    private function testUserImageCount()
    {
        echo "1. 测试获取user表图片总数...\n";
        
        try {
            $totalImages = $this->service->getTotalImageCount('user');
            echo "   User表需要迁移的图片总数: {$totalImages}\n";
            
            if ($totalImages >= 0) {
                echo "   ✓ 获取user表图片总数功能正常\n";
            } else {
                echo "   ✗ 获取user表图片总数异常\n";
            }
        } catch (\Exception $e) {
            echo "   ✗ 获取user表图片总数失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试示例user图片
     */
    private function testSampleUserImages()
    {
        echo "2. 测试示例user图片...\n";
        
        try {
            // 获取所有可能包含图片的用户
            $allUsers = Db::name('user')
                ->where('face_url', '<>', '')
                ->where('face_url', 'not null')
                ->select();

            echo "   数据库中face_url字段不为空的用户总数: " . count($allUsers) . "\n";

            // 筛选出真正包含图片URL的用户
            $validUsers = [];
            foreach ($allUsers as $user) {
                $imageUrl = trim($user['face_url']);
                if (!empty($imageUrl) && filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                    $validUsers[] = $user;
                }
            }

            echo "   真正包含图片的用户数量: " . count($validUsers) . "\n";

            if (empty($validUsers)) {
                echo "   ⚠️  没有找到包含有效图片的用户\n";
                echo "   建议检查数据库中的用户数据\n\n";
                return;
            }

            // 显示前5条真正包含图片的用户
            $displayUsers = array_slice($validUsers, 0, 5);
            
            foreach ($displayUsers as $user) {
                echo "   用户ID: {$user['id']} (手机: {$user['phone']})\n";
                
                $imageUrl = trim($user['face_url']);
                $isCos = $this->checkIfCosPath($imageUrl);
                echo "     头像: {$imageUrl}\n";
                echo "     状态: " . ($isCos ? 'COS路径' : '本地路径') . "\n";
                echo "     状态码: {$user['status_code']}\n";
                echo "\n";
            }

        } catch (\Exception $e) {
            echo "   ✗ 测试示例user图片失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 测试相关表的记录数量
     */
    private function testRelatedTablesCount()
    {
        echo "3. 测试相关表的记录数量...\n";
        
        try {
            // 获取一个有头像的用户
            $user = Db::name('user')
                ->where('face_url', '<>', '')
                ->where('face_url', 'not null')
                ->find();

            if (!$user) {
                echo "   ⚠️  没有找到包含头像的用户\n\n";
                return;
            }

            $userId = $user['id'];
            echo "   测试用户ID: {$userId}\n";
            echo "   用户头像: {$user['face_url']}\n\n";

            // 统计相关表的记录数
            $messageCount = Db::name('message')
                ->where('user_id', $userId)
                ->where('choose', '<', 100)
                ->count();
            echo "   该用户在message表的记录数(choose < 100): {$messageCount}\n";

            $commentCount = Db::name('comment')
                ->where('user_id', $userId)
                ->count();
            echo "   该用户在comment表的记录数: {$commentCount}\n";

            $postCount = Db::name('post')
                ->where('user_id', $userId)
                ->count();
            echo "   该用户在post表的记录数: {$postCount}\n";

            $totalRelated = $messageCount + $commentCount + $postCount;
            echo "   总计需要更新的相关记录数: {$totalRelated}\n";

            if ($totalRelated > 0) {
                echo "   ✓ 该用户迁移时将同时更新 {$totalRelated} 条相关记录\n";
            } else {
                echo "   ⚠️  该用户没有相关记录需要更新\n";
            }

        } catch (\Exception $e) {
            echo "   ✗ 测试相关表记录数量失败: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }

    /**
     * 检查是否是COS路径
     */
    private function checkIfCosPath($imageUrl)
    {
        return strpos($imageUrl, 'prod/') === 0 ||
               strpos($imageUrl, 'dev/') === 0 ||
               strpos($imageUrl, '.cos.') !== false ||
               strpos($imageUrl, 'treeholepublic-') !== false;
    }

    /**
     * 显示详细的user表统计信息
     */
    public function showDetailedStats()
    {
        echo "=== User表详细统计 ===\n\n";

        try {
            // 总用户数
            $totalUsers = Db::name('user')->count();
            echo "总用户数: {$totalUsers}\n";

            // 包含头像的用户数
            $withAvatarCount = Db::name('user')
                ->where('face_url', '<>', '')
                ->where('face_url', 'not null')
                ->count();
            echo "包含头像的用户数: {$withAvatarCount}\n";

            // 已迁移到COS的用户数
            $migratedCount = Db::name('user')
                ->where('face_url', 'like', '%cos.%')
                ->whereOr('face_url', 'like', 'prod/%')
                ->whereOr('face_url', 'like', 'dev/%')
                ->count();
            echo "已迁移到COS的用户数: {$migratedCount}\n";

            // 需要迁移的用户数
            $needMigrationCount = $this->service->getTotalImageCount('user');
            echo "需要迁移的头像数: {$needMigrationCount}\n";

            // 迁移进度
            $progress = $withAvatarCount > 0 ? round(($migratedCount / $withAvatarCount) * 100, 2) : 0;
            echo "迁移进度: {$progress}%\n";

        } catch (\Exception $e) {
            echo "获取统计信息失败: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }
}

// 运行测试
$test = new UserMigrationTest();

// 检查命令行参数
if (isset($argv[1]) && $argv[1] === '--stats') {
    $test->showDetailedStats();
} else {
    $test->runAllTests();
}
