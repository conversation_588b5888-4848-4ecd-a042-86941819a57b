<?php
/**
 * Notification表图片迁移调试脚本
 */

require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;

// 初始化ThinkPHP应用
$app = new App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');

echo "=== Notification表图片迁移调试 ===\n\n";

// 1. 检查总数
echo "1. 检查notification表总数...\n";
$totalCount = Db::name('notification')->count();
echo "   总通知数: {$totalCount}\n";

$withImageCount = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->count();
echo "   包含图片字段的通知数: {$withImageCount}\n\n";

// 2. 检查分页查询
echo "2. 检查分页查询...\n";
$limit = 100;
for ($offset = 0; $offset < min($withImageCount, 500); $offset += $limit) {
    $notifications = Db::name('notification')
        ->where('content_image', '<>', '')
        ->where('content_image', 'not null')
        ->limit($offset, $limit)
        ->select();
    
    echo "   偏移量 {$offset}: 查询到 " . count($notifications) . " 条记录\n";
    
    if (count($notifications) == 0) {
        echo "   ⚠️  查询结果为空，停止检查\n";
        break;
    }
}
echo "\n";

// 3. 检查图片URL格式
echo "3. 检查图片URL格式...\n";
$sampleNotifications = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->limit(10)
    ->select();

$urlPatterns = [];
foreach ($sampleNotifications as $notification) {
    $imageUrl = trim($notification['content_image']);
    
    // 分析URL模式
    if (strpos($imageUrl, 'https://www.bjgaoxiaoshequ.store/file/') === 0) {
        $urlPatterns['file'][] = $imageUrl;
    } elseif (strpos($imageUrl, 'https://www.bjgaoxiaoshequ.store/comment_image/') === 0) {
        $urlPatterns['comment_image'][] = $imageUrl;
    } elseif (strpos($imageUrl, 'prod/') === 0 || strpos($imageUrl, 'dev/') === 0 || strpos($imageUrl, '.cos.') !== false) {
        $urlPatterns['cos'][] = $imageUrl;
    } else {
        $urlPatterns['other'][] = $imageUrl;
    }
}

foreach ($urlPatterns as $pattern => $urls) {
    echo "   {$pattern} 模式: " . count($urls) . " 个\n";
    if (!empty($urls)) {
        echo "     示例: " . $urls[0] . "\n";
    }
}
echo "\n";

// 4. 检查已迁移的数量
echo "4. 检查已迁移的数量...\n";
$migratedCount = Db::name('notification')
    ->where(function($query) {
        $query->where('content_image', 'like', '%cos.%')
              ->whereOr('content_image', 'like', 'prod/%')
              ->whereOr('content_image', 'like', 'dev/%');
    })
    ->count();
echo "   已迁移到COS的通知数: {$migratedCount}\n";

$needMigrationCount = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->where('content_image', 'not like', '%cos.%')
    ->where('content_image', 'not like', 'prod/%')
    ->where('content_image', 'not like', 'dev/%')
    ->count();
echo "   需要迁移的通知数: {$needMigrationCount}\n\n";

// 5. 检查具体的未迁移图片
echo "5. 检查具体的未迁移图片...\n";
$unmigrated = Db::name('notification')
    ->where('content_image', '<>', '')
    ->where('content_image', 'not null')
    ->where('content_image', 'not like', '%cos.%')
    ->where('content_image', 'not like', 'prod/%')
    ->where('content_image', 'not like', 'dev/%')
    ->limit(5)
    ->select();

foreach ($unmigrated as $notification) {
    echo "   ID: {$notification['id']}, URL: {$notification['content_image']}\n";
    
    // 检查URL是否可访问
    $imageUrl = $notification['content_image'];
    $headers = @get_headers($imageUrl, 1);
    if ($headers && strpos($headers[0], '200') !== false) {
        echo "     ✓ URL可访问\n";
    } else {
        echo "     ✗ URL不可访问或超时\n";
    }
}
echo "\n";

// 6. 模拟分批处理逻辑
echo "6. 模拟分批处理逻辑...\n";
$limit = 100;
$offset = 0;
$totalProcessed = 0;
$batchCount = 0;

while (true) {
    $batchCount++;
    echo "   批次 {$batchCount}: 偏移量 {$offset}, 限制 {$limit}\n";
    
    $notifications = Db::name('notification')
        ->where('content_image', '<>', '')
        ->where('content_image', 'not null')
        ->limit($offset, $limit)
        ->select();
    
    $batchSize = count($notifications);
    echo "     查询到 {$batchSize} 条记录\n";
    
    if ($batchSize == 0) {
        echo "     没有更多数据，停止处理\n";
        break;
    }
    
    // 筛选需要迁移的记录
    $needMigration = 0;
    foreach ($notifications as $notification) {
        $imageUrl = trim($notification['content_image']);
        if (!empty($imageUrl) && 
            filter_var($imageUrl, FILTER_VALIDATE_URL) &&
            strpos($imageUrl, 'cos.') === false &&
            strpos($imageUrl, 'prod/') !== 0 &&
            strpos($imageUrl, 'dev/') !== 0) {
            $needMigration++;
        }
    }
    
    echo "     其中需要迁移: {$needMigration} 条\n";
    $totalProcessed += $needMigration;
    
    if ($batchSize < $limit) {
        echo "     批次大小({$batchSize})小于限制({$limit})，处理完成\n";
        break;
    }
    
    $offset += $limit;
    
    // 防止无限循环
    if ($batchCount > 20) {
        echo "     达到最大批次限制，停止模拟\n";
        break;
    }
}

echo "   模拟结果: 总共需要迁移 {$totalProcessed} 条记录\n";
echo "\n=== 调试完成 ===\n";
