<?php
/**
 * 图片迁移到COS脚本
 * 将message、comment、post表中的图片从本地服务器迁移到COS，并更新数据库中的路径
 *
 * 使用方法：
 * 本地环境：php migrate_message_images_to_cos.php [table]
 * 生产环境：php migrate_message_images_to_cos.php --production [table]
 *
 * 当前只支持notification表迁移（其他表已迁移完成）
 * 示例：
 * php migrate_message_images_to_cos.php --production notification # 迁移notification表
 */

// 解析命令行参数
$isProduction = false;
$targetTable = 'notification'; // 默认迁移notification表

if (isset($argv)) {
    foreach ($argv as $arg) {
        if ($arg === '--production' || $arg === '-p') {
            $isProduction = true;
        } elseif ($arg === 'notification') {
            $targetTable = $arg;
        }
    }
}

// 根据环境标志设置环境变量
if ($isProduction) {
    // 生产环境设置
    $_SERVER['HTTP_HOST'] = 'www.bjgaoxiaoshequ.store'; // 生产环境域名
    echo "=== 生产环境迁移模式 ===\n";
} else {
    // 本地环境设置
    $_SERVER['HTTP_HOST'] = 'localhost';
    echo "=== 本地环境迁移模式 ===\n";
}

// 设置其他必要的环境变量
$_SERVER['REQUEST_METHOD'] = 'GET';
$_SERVER['REQUEST_URI'] = '/';

// 显示环境信息
$currentHost = $_SERVER['HTTP_HOST'];
$isLocal = strpos($currentHost, 'localhost') !== false ||
           strpos($currentHost, '127.0.0.1') !== false ||
           strpos($currentHost, '192.168.') !== false;

echo "当前HOST: {$currentHost}\n";
echo "环境类型: " . ($isLocal ? '本地环境' : '生产环境') . "\n";
echo "命令行参数: " . ($isProduction ? '生产环境模式' : '本地环境模式') . "\n";
echo "目标表: {$targetTable}\n\n";

require_once __DIR__ . '/vendor/autoload.php';

// 初始化应用 - 使用正确的方式
$app = new think\App();
$app->initialize();

// 启动应用但不运行
$http = $app->http;
$request = $app->request;

// 确保配置已加载
$app->config->load($app->getConfigPath() . 'app.php', 'app');
$app->config->load($app->getConfigPath() . 'database.php', 'database');
$app->config->load($app->getConfigPath() . 'cos.php', 'cos');

use think\facade\Db;
use app\util\CosUtil;

class MessageImageMigrator
{
    private $baseUrl = 'https://www.bjgaoxiaoshequ.store'; // 你的域名
    private $migratedCount = 0;
    private $errorCount = 0;
    private $logFile;
    private $isLocal;
    private $skipExisting = true; // 是否跳过已经是COS路径的图片

    public function __construct($forceProduction = false)
    {
        $this->isLocal = !$forceProduction;
        $this->logFile = __DIR__ . '/logs/message_image_migration_' . date('Y-m-d_H-i-s') . '.log';
        
        // 确保日志目录存在
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $this->log("=== 环境信息 ===");
        $this->log("当前环境: " . ($this->isLocal ? "本地开发环境" : "生产环境"));
        $this->log("日志文件: " . $this->logFile);
    }

    /**
     * 开始迁移
     *
     * @param string $table 要迁移的表名
     */
    public function migrate($table = 'all')
    {
        $this->log("=== 开始图片迁移到COS ===");
        $this->log("目标表: {$table}");

        try {
            // 使用ImageMigrationService进行迁移
            $service = new \app\service\ImageMigrationService();

            if ($table !== 'notification') {
                $this->log("错误：当前只支持notification表迁移，其他表已迁移完成");
                return;
            }

            $this->log("开始迁移notification表的图片...");

            // 获取notification表的图片总数
            $totalCount = $service->getTotalImageCount($table);
            $this->log("notification表图片总数: {$totalCount}");

            // 一次性迁移所有记录
            $this->log("开始一次性处理所有记录...");
            $result = $service->migrateImages($table, 0, 0);

            if ($result['success']) {
                $queried = isset($result['queried']) ? $result['queried'] : $result['processed'];
                $this->log("迁移完成 - 查询: {$queried}, 筛选: {$result['processed']}, 迁移: {$result['migrated']}, 失败: {$result['errors']}");
                $this->migratedCount += $result['migrated'];
                $this->errorCount += $result['errors'];
            } else {
                $this->log("迁移失败: " . $result['message']);
            }

        } catch (\Exception $e) {
            $this->log("迁移过程中出错: " . $e->getMessage());
        }

        $this->log("=== 迁移完成 ===");
        $this->log("成功迁移: {$this->migratedCount} 个图片");
        $this->log("失败: {$this->errorCount} 个图片");
        
        echo "迁移完成！详细日志请查看: {$this->logFile}\n";
        echo "成功: {$this->migratedCount}, 失败: {$this->errorCount}\n";
    }






    /**
     * 记录日志
     */
    private function log($message)
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] {$message}\n";
        file_put_contents($this->logFile, $logMessage, FILE_APPEND);
        echo $logMessage;
    }
}

// 执行迁移
echo "=== 图片迁移到COS ===\n";
if ($isProduction) {
    echo "🌐 生产环境模式\n";
} else {
    echo "💻 本地环境模式\n";
}
echo "目标表: {$targetTable}\n";
echo "\n注意：此操作会修改数据库中的图片路径，建议先备份数据库！\n";
echo "使用方法：\n";
echo "  本地环境: php migrate_message_images_to_cos.php notification\n";
echo "  生产环境: php migrate_message_images_to_cos.php --production notification\n";
echo "  注意：其他表(message、comment、post)已迁移完成\n\n";
echo "按 Enter 继续，或 Ctrl+C 取消...\n";
readline();

$migrator = new MessageImageMigrator($isProduction);
$migrator->migrate($targetTable);
